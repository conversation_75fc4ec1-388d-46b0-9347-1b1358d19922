using UnityEngine;

namespace BTR.Debug
{
    /// <summary>
    /// Test script to verify BTR.Debug redirection system works correctly
    /// </summary>
    public class BTRDebugTest : MonoBehaviour
    {
        [ContextMenu("Test BTR.Debug System")]
        public void TestBTRDebugSystem()
        {
            // Test BTR.Debug calls
            BTR.Debug.Log.Write("BTR.Debug.Log test message");
            BTR.Debug.LogWarning.Write("BTR.Debug.LogWarning test message");
            BTR.Debug.LogError.Write("BTR.Debug.LogError test message");
            
            // Test with context
            BTR.Debug.Log.Write("BTR.Debug.Log with context", this);
            BTR.Debug.LogWarning.Write("BTR.Debug.LogWarning with context", this);
            BTR.Debug.LogError.Write("BTR.Debug.LogError with context", this);
            
            // Test global alias
            BTRDebug.Log.Write("BTRDebug alias test message");
            BTRDebug.LogWarning.Write("BTRDebug alias warning message");
            BTRDebug.LogError.Write("BTRDebug alias error message");
            
            Debug.Log("[BTRDebugTest] All BTR.Debug redirection tests completed successfully!");
        }
        
        private void Start()
        {
            // Automatic test on start
            Debug.Log("[BTRDebugTest] BTR.Debug redirection system initialized");
            
            #if UNITY_EDITOR
            // Run test automatically in editor
            TestBTRDebugSystem();
            #endif
        }
    }
}