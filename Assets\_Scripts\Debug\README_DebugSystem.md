# BTR Debug System

## Overview

The BTR project uses Unity's standard Debug system for all logging and debugging, with backward compatibility support for BTR.Debug calls. This provides a simple, reliable, and well-integrated debugging solution.

## Migration Complete + Backward Compatibility

**IMPORTANT**: The custom ConditionalDebug and BTRDebug systems have been completely removed and replaced with Unity's standard Debug class. However, BTR.Debug redirection is now available for backward compatibility.

## Current Debug Architecture

The debug system now uses Unity's built-in components with BTR.Debug redirection:

- **Unity Debug**: Standard Unity Debug.Log, Debug.LogWarning, Debug.LogError
- **BTR.Debug Redirection**: BTR.Debug.Log.Write(), BTR.Debug.LogError.Write(), BTR.Debug.LogWarning.Write() redirect to Unity Debug
- **Unity Console**: Built-in console window with filtering capabilities
- **Conditional Compilation**: Use `#if UNITY_EDITOR || DEVELOPMENT_BUILD` for development-only logs

## Using the Debug System

### Setup

No special setup is required. Unity's Debug system is available by default.

### Logging in Your Code

```csharp
// Unity Debug (Recommended)
Debug.Log("Regular log message");
Debug.Log($"[{GetType().Name}] Message with class context");
Debug.LogWarning("Warning message");
Debug.LogError("Error message");

// BTR.Debug (Backward Compatibility)
BTR.Debug.Log.Write("Regular log message");
BTR.Debug.LogError.Write("Error message");
BTR.Debug.LogWarning.Write("Warning message");

// Or using the global alias
BTRDebug.Log.Write("Regular log message");
BTRDebug.LogError.Write("Error message");
BTRDebug.LogWarning.Write("Warning message");

// Conditional compilation for development-only logs
#if UNITY_EDITOR || DEVELOPMENT_BUILD
Debug.Log($"[{GetType().Name}] Verbose debug information");
BTR.Debug.Log.Write($"[{GetType().Name}] BTR debug information");
#endif
```

### Unity Console Features

- **Filtering**: Use Unity Console's built-in filtering by message type, search text
- **Collapse**: Group identical messages together
- **Clear on Play**: Automatically clear console when entering play mode
- **Stack Traces**: Click on log entries to see stack traces

## Best Practices

### Consistent Logging Format
Use a consistent format for your debug messages:

```csharp
// Include class name for context
Debug.Log($"[{GetType().Name}] Your message here");

// For static methods, use the class name directly
Debug.Log($"[ClassName] Your message here");
```

### Conditional Compilation
For verbose debug logs that should only appear in development:

```csharp
#if UNITY_EDITOR || DEVELOPMENT_BUILD
Debug.Log($"[{GetType().Name}] Detailed debug information");
#endif
```

### Error Handling
Always use appropriate log levels:

```csharp
// For informational messages
Debug.Log($"[{GetType().Name}] Operation completed successfully");

// For potential issues
Debug.LogWarning($"[{GetType().Name}] Deprecated method used");

// For actual errors
Debug.LogError($"[{GetType().Name}] Failed to load resource: {resourceName}");
```

## Migration Notes

The BTR project has migrated from custom ConditionalDebug/BTRDebug systems to Unity's standard Debug system:

- **Removed**: ConditionalDebug, BTRDebug, DebugManager, DebugFilter classes
- **Replaced with**: Unity's Debug.Log, Debug.LogWarning, Debug.LogError
- **Benefits**: Simpler codebase, better Unity integration, standard debugging workflow

## Build Considerations

Unity's Debug calls are automatically stripped in release builds. For development builds, use:

```csharp
#if UNITY_EDITOR || DEVELOPMENT_BUILD
Debug.Log("Development-only message");
#endif
```

This ensures optimal performance in release builds while maintaining debugging capabilities during development.