using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using BTR;
using BTR.Core.Events;
using BTR.Core.Events.Categories;

namespace BTR.Debug
{
    /// <summary>
    /// Integration tester for Wave/Music synchronization with the Unified Event System
    /// Tests the complete flow from wave start to music changes to wave completion
    /// </summary>
    public class WaveMusicIntegrationTester : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private bool autoRunTests = false;
        [SerializeField] private float testDelay = 2f;
        
        [Header("Component References")]
        [SerializeField] private WaveSpawnController waveSpawnController;
        [SerializeField] private SceneManagerBTR sceneManagerBTR;
        [SerializeField] private WaveEventSubscriptions waveEventSubscriptions;
        
        [Header("Test Results")]
        [SerializeField] private List<string> testResults = new List<string>();
        [SerializeField] private int passedTests = 0;
        [SerializeField] private int failedTests = 0;
        
        // Event tracking
        private List<string> capturedEvents = new List<string>();
        private IUnifiedEventBus unifiedEventBus => UnifiedEventBus.Instance;
        
        private void Start()
        {
            if (autoRunTests)
            {
                StartCoroutine(RunIntegrationTestsCoroutine());
            }
        }
        
        [ContextMenu("Run Integration Tests")]
        public void RunIntegrationTests()
        {
            StartCoroutine(RunIntegrationTestsCoroutine());
        }
        
        private IEnumerator RunIntegrationTestsCoroutine()
        {
            LogTest("===== WAVE/MUSIC INTEGRATION TESTS STARTING =====");
            
            // Reset test state
            testResults.Clear();
            passedTests = 0;
            failedTests = 0;
            capturedEvents.Clear();
            
            // Subscribe to events for monitoring
            SubscribeToTestEvents();
            
            yield return new WaitForSeconds(1f);
            
            // Test 1: Component Discovery
            yield return StartCoroutine(TestComponentDiscovery());
            yield return new WaitForSeconds(testDelay);
            
            // Test 2: Event System Integration
            yield return StartCoroutine(TestEventSystemIntegration());
            yield return new WaitForSeconds(testDelay);
            
            // Test 3: Ouroboros Configuration
            yield return StartCoroutine(TestOuroborosConfiguration());
            yield return new WaitForSeconds(testDelay);
            
            // Test 4: Wave Start Flow
            yield return StartCoroutine(TestWaveStartFlow());
            yield return new WaitForSeconds(testDelay);
            
            // Test 5: Music Synchronization
            yield return StartCoroutine(TestMusicSynchronization());
            yield return new WaitForSeconds(testDelay);
            
            // Test 6: Duplicate Event Detection
            yield return StartCoroutine(TestDuplicateEventDetection());
            yield return new WaitForSeconds(testDelay);
            
            // Unsubscribe from events
            UnsubscribeFromTestEvents();
            
            // Report results
            LogTest("===== INTEGRATION TEST RESULTS =====");
            LogTest($"PASSED: {passedTests}");
            LogTest($"FAILED: {failedTests}");
            LogTest($"TOTAL EVENTS CAPTURED: {capturedEvents.Count}");
            
            foreach (string result in testResults)
            {
                LogTest(result);
            }
            
            LogTest("===== INTEGRATION TESTS COMPLETE =====");
        }
        
        private IEnumerator TestComponentDiscovery()
        {
            LogTest("TEST 1: Component Discovery");
            
            // Find components if not assigned
            if (waveSpawnController == null)
                waveSpawnController = FindFirstObjectByType<WaveSpawnController>();
            if (sceneManagerBTR == null)
                sceneManagerBTR = SceneManagerBTR.Instance;
            if (waveEventSubscriptions == null)
                waveEventSubscriptions = FindFirstObjectByType<WaveEventSubscriptions>();
            
            bool allFound = true;
            
            if (waveSpawnController == null)
            {
                RecordTestResult("FAIL: WaveSpawnController not found");
                allFound = false;
            }
            else
            {
                RecordTestResult($"PASS: WaveSpawnController found: {waveSpawnController.name}");
            }
            
            if (sceneManagerBTR == null)
            {
                RecordTestResult("FAIL: SceneManagerBTR not found");
                allFound = false;
            }
            else
            {
                RecordTestResult($"PASS: SceneManagerBTR found: {sceneManagerBTR.name}");
            }
            
            if (waveEventSubscriptions == null)
            {
                RecordTestResult("FAIL: WaveEventSubscriptions not found");
                allFound = false;
            }
            else
            {
                RecordTestResult($"PASS: WaveEventSubscriptions found: {waveEventSubscriptions.name}");
            }
            
            if (allFound)
            {
                RecordTestResult("PASS: All required components found");
            }
            else
            {
                RecordTestResult("FAIL: Missing required components");
            }
            
            yield return null;
        }
        
        private IEnumerator TestEventSystemIntegration()
        {
            LogTest("TEST 2: Event System Integration");
            
            bool eventBusFound = unifiedEventBus != null;
            
            if (eventBusFound)
            {
                RecordTestResult("PASS: UnifiedEventBus found");
                
                // Test event subscription
                try
                {
                    unifiedEventBus.Subscribe<WaveStartedEvent>(OnTestWaveStarted);
                    unifiedEventBus.Unsubscribe<WaveStartedEvent>(OnTestWaveStarted);
                    RecordTestResult("PASS: Event subscription/unsubscription works");
                }
                catch (System.Exception e)
                {
                    RecordTestResult($"FAIL: Event subscription error: {e.Message}");
                }
            }
            else
            {
                RecordTestResult("FAIL: UnifiedEventBus not found");
            }
            
            yield return null;
        }
        
        private IEnumerator TestOuroborosConfiguration()
        {
            LogTest("TEST 3: Ouroboros Configuration");
            
            if (sceneManagerBTR != null && sceneManagerBTR.currentGroup != null)
            {
                var currentGroup = sceneManagerBTR.currentGroup;
                RecordTestResult($"PASS: SceneGroup found: {currentGroup.name}");
                
                if (currentGroup.scenes != null && currentGroup.scenes.Length > 0)
                {
                    RecordTestResult($"PASS: {currentGroup.scenes.Length} scenes configured");
                    
                    // Check current scene configuration
                    int currentSceneIndex = sceneManagerBTR.CurrentSceneIndex;
                    if (currentSceneIndex < currentGroup.scenes.Length)
                    {
                        var currentScene = currentGroup.scenes[currentSceneIndex];
                        RecordTestResult($"PASS: Current scene: {currentScene.sceneName}");
                        
                        if (currentScene.songSections != null && currentScene.songSections.Length > 0)
                        {
                            RecordTestResult($"PASS: {currentScene.songSections.Length} song sections configured");
                            
                            // Log section details
                            for (int i = 0; i < currentScene.songSections.Length; i++)
                            {
                                var section = currentScene.songSections[i];
                                string marker = (i == sceneManagerBTR.CurrentSectionIndex) ? " <-- CURRENT" : "";
                                LogTest($"  Section {i}: '{section.name}' (FMOD: {section.section}, Waves: {section.waves}){marker}");
                            }
                        }
                        else
                        {
                            RecordTestResult("FAIL: No song sections configured");
                        }
                    }
                    else
                    {
                        RecordTestResult($"FAIL: Invalid scene index {currentSceneIndex}");
                    }
                }
                else
                {
                    RecordTestResult("FAIL: No scenes configured in SceneGroup");
                }
            }
            else
            {
                RecordTestResult("FAIL: SceneManagerBTR or currentGroup not found");
            }
            
            yield return null;
        }
        
        private IEnumerator TestWaveStartFlow()
        {
            LogTest("TEST 4: Wave Start Flow");
            
            if (waveSpawnController != null)
            {
                // Check wave configuration
                if (waveSpawnController.waveConfig != null)
                {
                    RecordTestResult($"PASS: Wave configuration found: {waveSpawnController.waveConfig.name}");
                    
                    // Check current wave state
                    int currentWave = waveSpawnController.CurrentWave;
                    int totalWaves = waveSpawnController.TotalWavesCount;
                    
                    RecordTestResult($"INFO: Current Wave: {currentWave}, Total Waves: {totalWaves}");
                    
                    // Test wave start simulation
                    LogTest("Simulating wave start...");
                    capturedEvents.Clear();
                    
                    // This should trigger the event flow
                    if (waveSpawnController.CurrentState != null)
                    {
                        RecordTestResult("PASS: Wave state is initialized");
                    }
                    else
                    {
                        RecordTestResult("WARN: Wave state not initialized");
                    }
                }
                else
                {
                    RecordTestResult("FAIL: No wave configuration assigned");
                }
            }
            else
            {
                RecordTestResult("FAIL: WaveSpawnController not available");
            }
            
            yield return null;
        }
        
        private IEnumerator TestMusicSynchronization()
        {
            LogTest("TEST 5: Music Synchronization");
            
            if (sceneManagerBTR != null)
            {
                // Check AudioManager integration
                if (AudioManager.Instance != null)
                {
                    RecordTestResult("PASS: AudioManager found");
                    
                    // Check current music section
                    string currentSectionName = sceneManagerBTR.GetCurrentSongSectionName();
                    RecordTestResult($"INFO: Current music section: '{currentSectionName}'");
                    
                    // Test music section update
                    LogTest("Testing music section update...");
                    
                    // This should trigger UpdateMusicSection() with debug logging
                    try
                    {
                        // Force a music update to see the debug flow
                        sceneManagerBTR.GetType().GetMethod("UpdateMusicSection", 
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                            ?.Invoke(sceneManagerBTR, null);
                        
                        RecordTestResult("PASS: Music section update method called");
                    }
                    catch (System.Exception e)
                    {
                        RecordTestResult($"FAIL: Music section update error: {e.Message}");
                    }
                }
                else
                {
                    RecordTestResult("FAIL: AudioManager not found");
                }
            }
            else
            {
                RecordTestResult("FAIL: SceneManagerBTR not available");
            }
            
            yield return null;
        }
        
        private IEnumerator TestDuplicateEventDetection()
        {
            LogTest("TEST 6: Duplicate Event Detection");
            
            // Check for potential duplicate event sources
            bool duplicateRisk = false;
            
            // Check if WaveSpawnController publishes WaveStartedEvent
            if (waveSpawnController != null)
            {
                LogTest("Checking WaveSpawnController event publishing...");
                // This is checked by looking at the SetNextWaveState method
                RecordTestResult("INFO: WaveSpawnController publishes WaveStartedEvent in SetNextWaveState()");
            }
            
            // Check if WaveEventSubscriptions also publishes WaveStartedEvent
            if (waveEventSubscriptions != null)
            {
                LogTest("Checking WaveEventSubscriptions event publishing...");
                RecordTestResult("WARN: WaveEventSubscriptions also tries to publish WaveStartedEvent");
                RecordTestResult("WARN: This creates duplicate events - one source should be disabled");
                duplicateRisk = true;
            }
            
            // Check SceneManagerBTR music event conflicts
            LogTest("Checking music event conflicts...");
            RecordTestResult("WARN: WaveSpawnController.PublishMusicChangeEvent() conflicts with SceneManagerBTR");
            RecordTestResult("WARN: WaveSpawnController uses hardcoded sections, SceneManagerBTR uses Ouroboros.asset");
            duplicateRisk = true;
            
            if (duplicateRisk)
            {
                RecordTestResult("FAIL: Duplicate event risks detected");
            }
            else
            {
                RecordTestResult("PASS: No duplicate event risks detected");
            }
            
            yield return null;
        }
        
        private void SubscribeToTestEvents()
        {
            if (unifiedEventBus != null)
            {
                unifiedEventBus.Subscribe<WaveStartedEvent>(OnTestWaveStarted);
                unifiedEventBus.Subscribe<WaveCompletedEvent>(OnTestWaveCompleted);
                unifiedEventBus.Subscribe<AudioMusicChangeRequestEvent>(OnTestMusicChange);
            }
        }
        
        private void UnsubscribeFromTestEvents()
        {
            if (unifiedEventBus != null)
            {
                unifiedEventBus.Unsubscribe<WaveStartedEvent>(OnTestWaveStarted);
                unifiedEventBus.Unsubscribe<WaveCompletedEvent>(OnTestWaveCompleted);
                unifiedEventBus.Unsubscribe<AudioMusicChangeRequestEvent>(OnTestMusicChange);
            }
        }
        
        private void OnTestWaveStarted(WaveStartedEvent waveEvent)
        {
            string eventInfo = $"WaveStartedEvent: Wave {waveEvent.WaveNumber} from {waveEvent.Source}";
            capturedEvents.Add(eventInfo);
            LogTest($"CAPTURED: {eventInfo}");
        }
        
        private void OnTestWaveCompleted(WaveCompletedEvent waveEvent)
        {
            string eventInfo = $"WaveCompletedEvent: Wave {waveEvent.WaveNumber} from {waveEvent.Source}";
            capturedEvents.Add(eventInfo);
            LogTest($"CAPTURED: {eventInfo}");
        }
        
        private void OnTestMusicChange(AudioMusicChangeRequestEvent musicEvent)
        {
            string eventInfo = $"AudioMusicChangeRequestEvent: {musicEvent.NewTrackId} from {musicEvent.Source}";
            capturedEvents.Add(eventInfo);
            LogTest($"CAPTURED: {eventInfo}");
        }
        
        private void RecordTestResult(string result)
        {
            testResults.Add(result);
            
            if (result.StartsWith("PASS"))
            {
                passedTests++;
            }
            else if (result.StartsWith("FAIL"))
            {
                failedTests++;
            }
            
            LogTest(result);
        }
        
        private void LogTest(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[INTEGRATION_TEST] {message}");
            }
        }
        
        [ContextMenu("Clear Test Results")]
        public void ClearTestResults()
        {
            testResults.Clear();
            capturedEvents.Clear();
            passedTests = 0;
            failedTests = 0;
        }
        
        [ContextMenu("Show Captured Events")]
        public void ShowCapturedEvents()
        {
            LogTest("===== CAPTURED EVENTS =====");
            foreach (string eventInfo in capturedEvents)
            {
                LogTest(eventInfo);
            }
            LogTest($"Total Events: {capturedEvents.Count}");
        }
    }
}