using System.Collections;
using Febucci.UI;
using TMPro; // Changed from UnityEngine.UI to TMPro
using UnityEngine;
using BTR.Core.Events;
using BTR.Core.Events.Categories;

namespace UltimateSpawner.Demo
{
    public class WaveHUDTimed : MonoBehaviour
    {
        // Private
        private BTR.WaveSpawnController waveController;
        private int currentWaveNumber = -1;
        private UnifiedEventBus _eventBus;

        // Public
        public TextMeshProUGUI waveText; // Changed from TextAnimatorPlayer to TextMeshProUGUI
        public TextMeshProUGUI nextWaveText; // Changed from Text to TextMeshProUGUI
        public float waveTextDisplayTime = 2.0f; // Set this value in Inspector

        private const float CONTROLLER_CHECK_INTERVAL = 0.5f; // Interval to check for the controller

        // Methods
        public void Start()
        {
            InitializeUnifiedEvents();
            StartCoroutine(InitializeWaveController());
        }

        private void InitializeUnifiedEvents()
        {
            _eventBus = UnifiedEventBus.Instance;
            if (_eventBus != null)
            {
                _eventBus.Subscribe<WaveStartedEvent>(OnWaveStartedUnified);
                _eventBus.Subscribe<WaveCompletedEvent>(OnWaveCompletedUnified);
            }
        }

        private IEnumerator InitializeWaveController()
        {
            while (waveController == null)
            {
                waveController = FindObjectOfType<BTR.WaveSpawnController>();
                if (waveController != null)
                {
                    // Legacy Unity Event subscription removed - now using unified events
                    break;
                }
                yield return new WaitForSeconds(CONTROLLER_CHECK_INTERVAL);
            }
        }

        public void Update()
        {
            if (waveController != null)
            {
                if (waveController.CurrentWave != currentWaveNumber)
                {
                    currentWaveNumber = waveController.CurrentWave;

                    if (currentWaveNumber < 1)
                    {
                        waveText.enabled = false;
                    }
                    else
                    {
                        waveText.enabled = true;
                        waveText.text = "<shake>ACTIVATE</shake>"; // Directly set text
                        StartCoroutine(DisableWaveTextAfterTime(waveTextDisplayTime));

                        OnWaveStarted();
                    }
                }
            }
        }

        private void OnWaveStarted()
        {
            if (currentWaveNumber > 1)
                StartCoroutine(ShowNextWaveHint());
        }

        private IEnumerator ShowNextWaveHint()
        {
            nextWaveText.color = Color.white;
            nextWaveText.enabled = true;

            yield return new WaitForSeconds(waveTextDisplayTime);

            WaitForSeconds wait = new WaitForSeconds(0.1f);

            Color temp = nextWaveText.color;

            while (temp.a > 0)
            {
                temp.a -= 0.05f;
                nextWaveText.color = temp;

                yield return wait;
            }
        }

        private IEnumerator DisableWaveTextAfterTime(float time)
        {
            yield return new WaitForSeconds(time);
            waveText.enabled = false;
        }

        private void OnDestroy()
        {
            if (_eventBus != null)
            {
                _eventBus.Unsubscribe<WaveStartedEvent>(OnWaveStartedUnified);
                _eventBus.Unsubscribe<WaveCompletedEvent>(OnWaveCompletedUnified);
            }
        }

        #region Unified Event Handlers

        private void OnWaveStartedUnified(WaveStartedEvent waveEvent)
        {
            currentWaveNumber = waveEvent.WaveNumber;

            if (currentWaveNumber < 1)
            {
                waveText.enabled = false;
            }
            else
            {
                waveText.enabled = true;
                waveText.text = "<shake>ACTIVATE</shake>";
                StartCoroutine(DisableWaveTextAfterTime(waveTextDisplayTime));

                // Publish UI event for wave display
                if (_eventBus != null)
                {
                    var uiEvent = new UIElementStateChangedEvent(
                        "WaveText",
                        "WaveHUD",
                        UIElementState.Hidden,
                        UIElementState.Visible,
                        $"Wave {waveEvent.WaveNumber} started",
                        "WaveHUDTimed"
                    );
                    _eventBus.Publish(uiEvent);
                }

                OnWaveStarted();
            }
        }

        private void OnWaveCompletedUnified(WaveCompletedEvent waveEvent)
        {
            // Handle wave completion UI updates
            if (_eventBus != null)
            {
                var uiEvent = new UIElementStateChangedEvent(
                    "WaveText",
                    "WaveHUD",
                    UIElementState.Visible,
                    UIElementState.Hidden,
                    $"Wave {waveEvent.WaveNumber} completed",
                    "WaveHUDTimed"
                );
                _eventBus.Publish(uiEvent);
            }
        }

        #endregion
    }
}
