// Global using directives for the entire project
// This file provides global aliases and using statements that apply to all C# files in the project

// Global alias to ensure Debug resolves to UnityEngine.Debug
// This allows all files to use Debug.Log without namespace conflicts
global using Debug = UnityEngine.Debug;

global using Object = UnityEngine.Object;

// BTR.Debug namespace redirection for backward compatibility
// This allows BTR.Debug.Log, BTR.Debug.LogError, BTR.Debug.LogWarning to work
global using BTRDebug = BTR.Debug;
