using UnityEngine;
using BTR;
using UltimateSpawner;
using BTR.Core.Events;
using BTR.Core.Events.Categories;

namespace BTR
{
    public class WaveEventSubscriptions : MonoBehaviour
    {
        [SerializeField]
        private bool enableDebugLogs = true;
        [SerializeField] private WaveEventChannel waveEventChannel;

        private BTR.WaveSpawnController waveSpawnController;
        private GameEvents gameEvents => GameEventsManager.Instance?.Events;
        private SplineManager splineManager;
        private int currentWaveNumber = 0;
        
        // Unified Event Bus integration
        private IUnifiedEventBus unifiedEventBus => UnifiedEventBus.Instance;

        private void Start()
        {
            ValidateConfiguration();
            FindAndSubscribeToComponents();
            SubscribeToEvents();
        }

        private void ValidateConfiguration()
        {
            bool isValid = true;

            if (waveEventChannel == null)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] on {gameObject.name}: WaveEventChannel not assigned!", this);
                isValid = false;
            }

            // Legacy event managers no longer needed - using unified events

            // Check for SceneManagerBTR early
            if (SceneManagerBTR.Instance == null)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] on {gameObject.name}: SceneManagerBTR.Instance is null! Wave events will not trigger scene transitions.", this);
                isValid = false;
            }

            if (!isValid)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] on {gameObject.name}: Configuration validation failed! Wave system may not work correctly.", this);
                return;
            }

            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] on {gameObject.name}: Configuration validated successfully.", this);
        }

        private void OnDestroy()
        {
            UnsubscribeEvents();
        }

        private void FindAndSubscribeToComponents()
        {
            waveSpawnController = GetComponent<BTR.WaveSpawnController>();
            if (waveSpawnController != null)
            {
                waveSpawnController.OnWaveStarted.AddListener(HandleWaveSpawnControllerStarted);
                waveSpawnController.OnWaveEnded.AddListener(HandleWaveSpawnControllerEnded);
                waveSpawnController.OnEnemySpawned += HandleEnemySpawned;
                waveSpawnController.OnWaveCustomEvent.AddListener(HandleWaveCustomEvent);
            }
            else
            {
                if (enableDebugLogs)
                {
                    Debug.LogError($"[{GetType().Name}] WaveSpawnController not found!");
                }
            }

            splineManager = FindObjectOfType<SplineManager>();
            if (splineManager == null)
            {
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[{GetType().Name}] SplineManager not found!");
                }
            }
            else
            {
                // Ensure the SplineManager is properly initialized
                if (splineManager.currSpline == 0)
                {
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[{GetType().Name}] [WAVE] Initializing SplineManager from WaveEventSubscriptions");
                    }
                    splineManager.InitializeSplineSystem();
                }
            }
        }

        private void SubscribeToEvents()
        {
            // MIGRATION NOTE: Legacy event subscriptions removed in Phase 1 of unified event system migration
            // All wave events now handled exclusively through the unified event system
            // Legacy handlers (HandleWaveStarted, HandleWaveCompleted, HandleSectionStarted, HandleSectionCompleted) removed
            
            // Unified Event Bus subscriptions - now the primary event system
            if (unifiedEventBus != null)
            {
                unifiedEventBus.Subscribe<WaveStartedEvent>(OnWaveStartedUnified);
                unifiedEventBus.Subscribe<WaveCompletedEvent>(OnWaveCompletedUnified);
                unifiedEventBus.Subscribe<EnemySpawnedEvent>(OnEnemySpawnedUnified);
                unifiedEventBus.Subscribe<EnemyDeathEvent>(OnEnemyDeathUnified);
                unifiedEventBus.Subscribe<GameStartedEvent>(OnGameStarted);
                unifiedEventBus.Subscribe<GamePausedEvent>(OnGamePaused);
                unifiedEventBus.Subscribe<GameResumedEvent>(OnGameResumed);
                unifiedEventBus.Subscribe<AudioMusicChangeRequestEvent>(OnMusicChangeRequested);

                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Subscribed to enhanced unified wave events");
                }
            }
        }

        private void UnsubscribeEvents()
        {
            if (waveSpawnController != null)
            {
                waveSpawnController.OnWaveStarted.RemoveListener(HandleWaveSpawnControllerStarted);
                waveSpawnController.OnWaveEnded.RemoveListener(HandleWaveSpawnControllerEnded);
                waveSpawnController.OnEnemySpawned -= HandleEnemySpawned;
                waveSpawnController.OnWaveCustomEvent.RemoveListener(HandleWaveCustomEvent);
            }

            // MIGRATION NOTE: Legacy event unsubscriptions removed - no longer needed
            // waveEventChannel legacy event handlers removed in Phase 1 migration
            
            // Unified Event Bus unsubscriptions - now the primary event system
            if (unifiedEventBus != null)
            {
                unifiedEventBus.Unsubscribe<WaveStartedEvent>(OnWaveStartedUnified);
                unifiedEventBus.Unsubscribe<WaveCompletedEvent>(OnWaveCompletedUnified);
                unifiedEventBus.Unsubscribe<EnemySpawnedEvent>(OnEnemySpawnedUnified);
                unifiedEventBus.Unsubscribe<EnemyDeathEvent>(OnEnemyDeathUnified);
                unifiedEventBus.Unsubscribe<GameStartedEvent>(OnGameStarted);
                unifiedEventBus.Unsubscribe<GamePausedEvent>(OnGamePaused);
                unifiedEventBus.Unsubscribe<GameResumedEvent>(OnGameResumed);
                unifiedEventBus.Unsubscribe<AudioMusicChangeRequestEvent>(OnMusicChangeRequested);
                
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Unsubscribed from unified wave events (legacy events removed)");
                }
            }
        }

        private void HandleWaveSpawnControllerEnded()
        {
            Debug.Log($"[INTEGRATION_DEBUG] ===== WAVE END EVENT FLOW =====");
            Debug.Log($"[INTEGRATION_DEBUG] HandleWaveSpawnControllerEnded: Wave {currentWaveNumber}");
            Debug.Log($"[INTEGRATION_DEBUG] Source: Legacy Unity Event from WaveSpawnController");
            
            // CRITICAL ISSUE: This might create duplicate WaveCompletedEvent!
            // Check if WaveSpawnController already publishes WaveCompletedEvent
            Debug.LogWarning($"[INTEGRATION_ISSUE] POTENTIAL DUPLICATE: WaveEventSubscriptions publishing WaveCompletedEvent");
            Debug.LogWarning($"[INTEGRATION_ISSUE] Check if WaveSpawnController also publishes this event!");
            
            // TEMPORARILY DISABLE to prevent potential duplicates
            /*
            // Publish unified wave completed event
            if (unifiedEventBus != null)
            {
                var waveEvent = new WaveCompletedEvent(
                    currentWaveNumber,
                    Time.time, // completion time
                    0, // enemies killed (would need tracking)
                    0, // enemies escaped (would need tracking)
                    0, // bonus score (would need calculation)
                    "WaveEventSubscriptions"
                );
                unifiedEventBus.Publish(waveEvent);
                
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Published unified WaveCompletedEvent for wave {currentWaveNumber}");
            }
            */
            
            // Let the controller handle the next wave directly
            if (waveSpawnController != null)
            {
                Debug.Log($"[INTEGRATION_DEBUG] Triggering waveEventChannel.TriggerWaveCompleted({currentWaveNumber})");
                waveEventChannel.TriggerWaveCompleted(currentWaveNumber);
                Debug.Log($"[INTEGRATION_DEBUG] WaveCompleted event triggered through channel");
            }
            
            Debug.Log($"[INTEGRATION_DEBUG] ===== WAVE END EVENT FLOW COMPLETE =====");
        }

        private void HandleWaveSpawnControllerStarted()
        {
            currentWaveNumber++;
            
            Debug.Log($"[INTEGRATION_DEBUG] ===== WAVE START EVENT FLOW =====");
            Debug.Log($"[INTEGRATION_DEBUG] HandleWaveSpawnControllerStarted: Wave {currentWaveNumber}");
            Debug.Log($"[INTEGRATION_DEBUG] Source: Legacy Unity Event from WaveSpawnController");
            
            // CRITICAL ISSUE: This creates duplicate WaveStartedEvent!
            // WaveSpawnController already publishes WaveStartedEvent in SetNextWaveState()
            Debug.LogWarning($"[INTEGRATION_ISSUE] DUPLICATE EVENT: WaveEventSubscriptions is about to publish WaveStartedEvent");
            Debug.LogWarning($"[INTEGRATION_ISSUE] But WaveSpawnController.SetNextWaveState() already published one!");
            Debug.LogWarning($"[INTEGRATION_ISSUE] This causes duplicate event handling throughout the system");

            // TEMPORARILY DISABLE to prevent duplicates
            /*
            // Publish unified wave started event
            if (unifiedEventBus != null)
            {
                var waveEvent = new WaveStartedEvent(
                    currentWaveNumber,
                    0, // total enemies (would need to be populated from wave data)
                    1.0f, // difficulty multiplier
                    0f, // wave duration (unknown at start)
                    new string[0], // enemy types (would need to be populated)
                    "WaveEventSubscriptions"
                );
                unifiedEventBus.Publish(waveEvent);
                
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Published unified WaveStartedEvent for wave {currentWaveNumber}");
            }
            */

            // Trigger GameEvents for audio system
            gameEvents?.TriggerWaveStarted(currentWaveNumber);

            // Enhanced validation and communication with SceneManagerBTR
            if (SceneManagerBTR.Instance == null)
            {
                Debug.LogError($"[INTEGRATION_DEBUG] SceneManagerBTR.Instance is null! Wave events will not be processed correctly.");
            }
            else
            {
                Debug.Log($"[INTEGRATION_DEBUG] Calling SceneManagerBTR.updateStatus('wavestart') - but this is IGNORED by design");
                SceneManagerBTR.Instance.updateStatus("wavestart");
                Debug.Log($"[INTEGRATION_DEBUG] SceneManagerBTR.updateStatus() call completed (was ignored)");
            }

            // Notify managers that need to know about wave start
            Debug.Log($"[INTEGRATION_DEBUG] Notifying managers of wave start...");
            EnemyManager.Instance?.OnWaveStart(currentWaveNumber);
            ProjectileManager.Instance?.OnWaveStart();
            GameManager.Instance?.ClearAllPlayerLocks();
            PlayerLocking.Instance?.OnNewWaveOrAreaTransition();

            // Handle spline transitions - ONLY increment for first wave of a section
            if (ValidateSplineManager() && SceneManagerBTR.Instance != null)
            {
                int completedWaves = SceneManagerBTR.Instance.CompletedWaves;
                string sectionName = SceneManagerBTR.Instance.GetCurrentSongSectionName();
                
                Debug.Log($"[INTEGRATION_DEBUG] Spline Logic: CompletedWaves={completedWaves}, Section='{sectionName}'");
                
                // Only increment spline for the FIRST wave of a section (completedWaves == 0)
                if (completedWaves == 0)
                {
                    Debug.Log($"[INTEGRATION_DEBUG] First wave of section '{sectionName}' - incrementing spline for wave {currentWaveNumber}");
                    
                    // Increment the spline only for section starts
                    splineManager.IncrementSpline();
                    
                    Debug.Log($"[INTEGRATION_DEBUG] Current spline after increment: {splineManager.currSpline}");
                }
                else
                {
                    Debug.Log($"[INTEGRATION_DEBUG] Subsequent wave {currentWaveNumber} in section '{sectionName}' ({completedWaves} waves already completed) - NOT incrementing spline");
                }
            }
            else
            {
                Debug.LogError("[INTEGRATION_DEBUG] Failed to validate SplineManager or SceneManagerBTR in HandleWaveSpawnControllerStarted");
            }
            
            Debug.Log($"[INTEGRATION_DEBUG] ===== WAVE START EVENT FLOW COMPLETE =====");
        }

        private void HandleEnemySpawned(Transform enemyTransform)
        {
            try
            {
                waveEventChannel.TriggerEnemySpawned(enemyTransform);
                
                // Legacy GameEvents call removed - now using unified events
                
                if (GameManager.Instance != null)
                {
                    GameManager.Instance.RegisterEnemy(enemyTransform);
                }

                // Add enemy to spatial grid via EnemyManager
                if (enemyTransform != null && enemyTransform.TryGetComponent<EnemyCore>(out var enemyCore))
                {
                    if (EnemyManager.Instance != null)
                    {
                        EnemyManager.Instance.RegisterEnemy(enemyCore);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[WAVE] Error during enemy spawn: {e.Message}\n{e.StackTrace}");
            }
        }

        // MIGRATION NOTE: Legacy event handlers removed in Phase 1 of unified event system migration
        // The following methods have been removed and their functionality moved to unified event handlers:
        // - HandleWaveStarted() -> functionality now in HandleWaveSpawnControllerStarted() and OnWaveStartedUnified()
        // - HandleWaveCompleted() -> functionality now in HandleWaveSpawnControllerEnded() and OnWaveCompletedUnified()
        // - HandleSectionStarted() -> functionality integrated into unified event system
        // - HandleSectionCompleted() -> functionality integrated into unified event system

        private void HandleWaveCustomEvent(string eventName)
        {
            Debug.Log($"[WAVE] Custom event received: {eventName}");
            waveEventChannel.TriggerWaveCustomEvent(eventName, currentWaveNumber);
        }

        // Add a method to verify SplineManager before using it
        private bool ValidateSplineManager()
        {
            if (splineManager == null)
            {
                Debug.LogWarning("[WAVE] SplineManager is null, trying to find it");
                splineManager = FindObjectOfType<SplineManager>();

                if (splineManager == null)
                {
                    Debug.LogError("[WAVE] SplineManager not found!");
                    return false;
                }
            }

            // Ensure it's initialized
            splineManager.InitializeSplineSystem(); // This is safe as the method checks if already initialized
            return true;
        }
        
        #region Unified Event Handlers
        
        private void OnWaveStartedUnified(WaveStartedEvent waveEvent)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Unified wave started event: Wave {waveEvent.WaveNumber} with {waveEvent.TotalEnemies} enemies");
            }
            
            // Additional unified event processing can be added here
            // This runs alongside the legacy event handling
        }
        
        private void OnWaveCompletedUnified(WaveCompletedEvent waveEvent)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Unified wave completed event: Wave {waveEvent.WaveNumber}, {waveEvent.EnemiesKilled} enemies killed");
            }
            
            // Additional unified event processing can be added here
            // This runs alongside the legacy event handling
        }
        
        private void OnGameStarted(GameStartedEvent gameStartedEvent)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Game started event: {gameStartedEvent.GameMode} - Level {gameStartedEvent.LevelId}");
            }
            
            // Reset wave counter when game starts
            currentWaveNumber = 0;
        }
        
        private void OnGamePaused(GamePausedEvent gamePausedEvent)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Game paused: {gamePausedEvent.PauseReason}");
            }
            
            // Handle wave system pause logic if needed
        }
        
        private void OnGameResumed(GameResumedEvent gameResumedEvent)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Game resumed after {gameResumedEvent.PauseDuration}s");
            }

            // Handle wave system resume logic if needed
        }

        private void OnEnemySpawnedUnified(EnemySpawnedEvent spawnEvent)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Enemy spawned via unified events: {spawnEvent.EnemyType} at {spawnEvent.SpawnPosition} for Wave {spawnEvent.WaveNumber}");
            }

            // Register enemy with managers
            if (spawnEvent.EnemyGameObject != null)
            {
                // Register enemy with GameManager
                if (GameManager.Instance != null)
                {
                    GameManager.Instance.RegisterEnemy(spawnEvent.EnemyGameObject.transform);
                }

                // Add enemy to spatial grid via EnemyManager
                if (spawnEvent.EnemyGameObject.TryGetComponent<EnemyCore>(out var enemyCore))
                {
                    if (EnemyManager.Instance != null)
                    {
                        EnemyManager.Instance.RegisterEnemy(enemyCore);
                    }
                }
            }
        }

        private void OnEnemyDeathUnified(EnemyDeathEvent deathEvent)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Enemy death via unified events: {deathEvent.EnemyType} (ID: {deathEvent.EnemyId}), Score: {deathEvent.ScoreValue}");
            }

            // Handle enemy death logic, update wave progress
            // This could trigger wave completion checks
        }

        private void OnMusicChangeRequested(AudioMusicChangeRequestEvent musicEvent)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Music change requested: {musicEvent.NewTrackId} (Source: {musicEvent.Source})");
            }

            // Forward to audio system or handle music synchronization
            // This ensures wave progression is synchronized with music
        }

        #endregion
    }
}
