using System;
using System.Collections;
using UltimateSpawner;
using UnityEngine;
using BTR;
using UnityEngine.Events;

public class InitializeWave : MonoBehaviour
{
    [HideInInspector]
    public BTR.WaveSpawnController waveSpawnController;
    
    [SerializeField] private int startWave = 0; // Changed default from 99 to 0 for normal progression
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool onlyInDevelopmentBuilds = true; // Only override wave in development

    void Start()
    {
        ValidateConfiguration();
        
        // Automatically grab the WaveSpawnController component from the same GameObject
        waveSpawnController = GetComponent<BTR.WaveSpawnController>();

        if (waveSpawnController != null)
        {
            // Check if we should override the wave start
            bool shouldOverride = !onlyInDevelopmentBuilds || Application.isEditor || Debug.isDebugBuild;
            
            if (shouldOverride && startWave != 0)
            {
                if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] on {gameObject.name}: OVERRIDING wave start to index {startWave} (Development mode)", this);
                waveSpawnController.StartWave(startWave);
            }
            else
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] on {gameObject.name}: Starting at normal wave progression (wave 0)", this);
                waveSpawnController.StartWave(0); // Always start at 0 for normal progression
            }
        }
        else
        {
            if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] on {gameObject.name}: WaveSpawnController is not assigned or not found on the GameObject!", this);
        }
    }

    private void ValidateConfiguration()
    {
        bool isValid = true;

        // Warn about unusual wave start values
        if (startWave > 10)
        {
            if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] on {gameObject.name}: startWave is set to {startWave} which is unusually high! This may skip important early sections.", this);
        }
        else if (startWave < 0)
        {
            if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] on {gameObject.name}: startWave cannot be negative! Setting to 0.", this);
            startWave = 0;
            isValid = false;
        }

        // Check if we're in a scene that likely needs normal progression
        var sceneManager = SceneManagerBTR.Instance;
        if (sceneManager != null && startWave != 0)
        {
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] on {gameObject.name}: SceneManagerBTR detected. Consider using startWave = 0 for proper scene progression.", this);
        }

        if (isValid && enableDebugLogs)
        {
            string mode = onlyInDevelopmentBuilds ? "Development only" : "Always active";
            Debug.Log($"[{GetType().Name}] on {gameObject.name}: Configuration validated. StartWave: {startWave} ({mode})", this);
        }
    }

    /// <summary>
    /// Manual method to start a specific wave for testing
    /// </summary>
    [ContextMenu("Start Wave 0 (Normal)")]
    public void StartWaveNormal()
    {
        if (waveSpawnController != null)
        {
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] on {gameObject.name}: Manual start at wave 0", this);
            waveSpawnController.StartWave(0);
        }
    }

    /// <summary>
    /// Manual method to start the configured wave for testing
    /// </summary>
    [ContextMenu("Start Configured Wave")]
    public void StartConfiguredWave()
    {
        if (waveSpawnController != null)
        {
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] on {gameObject.name}: Manual start at configured wave {startWave}", this);
            waveSpawnController.StartWave(startWave);
        }
    }
}
