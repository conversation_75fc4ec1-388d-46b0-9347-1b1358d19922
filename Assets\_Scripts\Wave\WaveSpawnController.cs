using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UltimateSpawner;
using UltimateSpawner.Waves;
using UltimateSpawner.Spawning;
using UltimateSpawner.Waves.Parameters;
using BTR.Core.Events;
using BTR.Core.Events.Categories;

namespace BTR
{
    /// <summary>
    /// Wave spawn controller that integrates UltimateSpawner with the BTR Unified Event System
    /// Handles wave progression, enemy tracking, and event publishing for music synchronization
    /// </summary>
    [Serializable]
    public class UnityStringEvent : UnityEvent<string> { }

    public class WaveSpawnController : SpawnController, IDisposable
    {
        #region Types
        public enum WaveNodeType
        {
            None = 0,
            Start,
            Condition,
            Delay,
            Event,
            Loop,
            Wave,
            SubWave,
        }
        #endregion

        #region Private Fields
        private WaveNode currentNode = null;
        private int currentWaveSpawnedItemCount = 0;
        private int currentWaveDestroyedItemCount = 0;
        private WaveNodeType currentNodeType = WaveNodeType.None;
        private WaveState currentWaveState = null;
        private float waveStartTime = 0f;
        private int lastWaveNumber = -1;
        private bool isDisposed = false;

        // Enemy tracking for wave completion
        private HashSet<int> spawnedEnemyIds = new HashSet<int>();
        private HashSet<int> killedEnemyIds = new HashSet<int>();
        #endregion

        #region Serialized Fields
        [SerializeField]
        private int currentWaveNumber;

        [SerializeField] 
        private bool enableDebugLogs = true;

        [Tooltip("The wave configuration asset to use for this wave spawn controller")]
        public WaveConfiguration waveConfig;
        #endregion

        #region Unity Events (for backward compatibility)
        [HideInInspector]
        public UnityEvent OnWaveStarted;

        [HideInInspector]
        public UnityEvent OnWaveEnded;

        [HideInInspector]
        public UnityStringEvent OnWaveCustomEvent;
        #endregion

        #region C# Events
        /// <summary>
        /// Fired when an enemy is spawned (for WaveEventSubscriptions compatibility)
        /// </summary>
        public event Action<Transform> OnEnemySpawned;
        #endregion

        #region Unified Event System Integration
        private IUnifiedEventBus unifiedEventBus => UnifiedEventBus.Instance;
        #endregion

        #region Properties
        public WaveState CurrentState => currentWaveState;

        public int CurrentWave
        {
            get 
            {
                if (currentWaveState == null)
                    return 0;
                return currentWaveState.WaveNumber; 
            }
        }

        public int TotalWavesCount
        {
            get
            {
                if (waveConfig == null)
                    return -1;
                return waveConfig.GetConnectedNodeCountOfType<WaveMasterNode>();
            }
        }

        public int CurrentWaveSpawnedItemCount => currentWaveSpawnedItemCount;
        public int CurrentWaveDestroyedItemCount => currentWaveDestroyedItemCount;
        public WaveNode CurrentNode => currentNode;
        public WaveNodeType CurrentNodeType => currentNodeType;

        public WaveParameterSet Parameters
        {
            get
            {
                if (waveConfig == null)
                    return null;

                WaveParameterNode parameterNode = waveConfig.GetParameterNode();
                if (parameterNode == null)
                    return null;

                return parameterNode.GetRuntimeParameters();
            }
        }
        #endregion

        #region Constructor
        public WaveSpawnController()
        {
            // Initialize without WaveState for now to avoid type conflicts
            // WaveState will be initialized when needed
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] WaveSpawnController initialized");
            }
        }
        #endregion

        #region Unity Lifecycle
        /// <summary>
        /// Called by Unity.
        /// </summary>
        public override void Awake()
        {
            base.Awake();

            if(waveConfig == null)
            {
                enabled = false;
                throw new MissingReferenceException($"Spawn controller '{GetType().Name}' requires a 'WaveConfiguration' to be assigned");
            }

            RegenerateWaveGraph();
            SubscribeToEnemyDeathEvents();

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Initialized with wave config: {waveConfig.name}");
            }
        }

        private void OnDestroy()
        {
            Dispose();
        }
        #endregion

        #region IDisposable Implementation
        public void Dispose()
        {
            if (!isDisposed)
            {
                UnsubscribeFromEnemyDeathEvents();
                spawnedEnemyIds?.Clear();
                killedEnemyIds?.Clear();
                isDisposed = true;

                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Disposed and cleaned up resources");
                }
            }
        }
        #endregion

        #region Wave Configuration
        public void RegenerateWaveGraph()
        {
            if (waveConfig == null)
            {
                Debug.LogError("[WaveSpawnController] Wave configuration is not assigned.");
                return;
            }

            if (waveConfig.nodes == null)
            {
                Debug.LogError("[WaveSpawnController] Wave configuration nodes are not initialized.");
                return;
            }

            for (int i = 0; i < waveConfig.nodes.Count; i++)
            {
                var node = waveConfig.nodes[i];
                if (node != null)
                {
                    if (node is WaveNode waveNode)
                    {
                        waveNode.OnGenerateWaveSession();
                    }
                }
                else
                {
                    Debug.LogWarning("[WaveSpawnController] Encountered a null node in wave configuration.");
                }
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[WaveSpawnController] Regenerated wave graph with {waveConfig.nodes.Count} nodes");
            }
        }
        #endregion

        #region Wave Control Methods
        public void StartWave(int waveIndex)
        {
            currentNode = waveConfig.GetStartNode();
            int waveCounter = 0;

            while(currentNode != null)
            {
                if(currentNode is WaveMasterNode)
                {
                    if (waveCounter == waveIndex)
                        break;
                    waveCounter++;
                }
                currentNode = currentNode.GetConnectedOutNode();
            }

            if (currentNode != null)
                StartWaveNode(currentNode);
        }

        public void EndWave() 
        {
            OnWaveEndedInternal();
        }

        public void RestartWave() 
        {
            ResetWaveTracking();
            if (currentNode != null)
                StartWaveNode(currentNode);
        }

        public void NextWave() 
        {
            if (currentNode != null)
            {
                var nextNode = currentNode.GetConnectedOutNode();
                if (nextNode != null)
                    StartWaveNode(nextNode);
            }
        }

        public void PreviousWave() 
        {
            // Implementation would require tracking previous nodes
            Debug.LogWarning("[WaveSpawnController] PreviousWave not implemented - requires node history tracking");
        }
        #endregion

        #region Spawn Control Override
        public override IEnumerator SpawnRoutine()
        {
            if (waveConfig == null)
            {
                StopSpawning();
                yield break;
            }

            if(currentNode == null)
            {
                WaveStartNode startNode = waveConfig.GetStartNode();
                if(startNode == null)
                {
                    Debug.LogErrorFormat("[WaveSpawnController] Wave config '{0}' does not have a start node", waveConfig);
                    StopSpawning();
                    yield break;
                }

                currentNode = startNode;
                currentNodeType = WaveNodeType.Start;

                if (currentNode == null)
                    yield break;
            }

            // TEMPORARY FIX: Skip node evaluation to avoid type conflicts
            // This needs to be properly resolved by ensuring BTR.WaveSpawnController properly inherits from UltimateSpawner.SpawnController
            if (enableDebugLogs)
            {
                Debug.LogWarning($"[WaveSpawnController] INTEGRATION_ISSUE: Skipping node.Evaluate() due to type conflicts");
                Debug.LogWarning($"[WaveSpawnController] This needs to be fixed by properly implementing UltimateSpawner integration");
            }
            
            // For now, just simulate the spawn routine completion
            yield return new WaitForSeconds(0.1f);
            OnEnd.Invoke();
        }

        public override void ResetState()
        {
            base.ResetState();
            ResetWaveTracking();
        }
        #endregion

        #region Wave State Management
        public virtual WaveState OnAdvanceWave(WaveState lastWave, WaveState newWave)
        {
            return newWave;
        }

        internal void SetNextWaveState(WaveState state)
        {
            currentWaveState = OnAdvanceWave(currentWaveState, state);
            UpdateCurrentWaveNumber();

            if (currentWaveState != null && currentWaveState.WaveNumber != lastWaveNumber)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[WAVE_INTEGRATION_DEBUG] SetNextWaveState: Wave {currentWaveState.WaveNumber} starting");
                    Debug.Log($"[WAVE_INTEGRATION_DEBUG] Publishing WaveStartedEvent from WaveSpawnController");
                }
                
                PublishWaveStartedEvent(currentWaveState.WaveNumber);
                lastWaveNumber = currentWaveState.WaveNumber;
            }

            OnWillEnterNewWave();
        }

        private void StartWaveNode(WaveNode node)
        {
            if (node == null)
                return;

            currentNode = node;

            if(IsSpawning == false)
            {
                StartSpawning();
            }
        }

        private void OnWillEnterNewWave()
        {
            ResetWaveTracking();
            waveStartTime = Time.time;

            // Trigger Unity event for backward compatibility
            OnWaveStarted?.Invoke();

            if (enableDebugLogs)
            {
                Debug.Log($"[WaveSpawnController] Starting new wave {CurrentWave}");
            }
        }

        private void ResetWaveTracking()
        {
            currentWaveSpawnedItemCount = 0;
            currentWaveDestroyedItemCount = 0;
            spawnedEnemyIds.Clear();
            killedEnemyIds.Clear();
        }

        private void UpdateCurrentWaveNumber()
        {
            currentWaveNumber = CurrentWave;
        }
        #endregion

        #region Enemy Tracking
        protected override void OnControllerSpawnedItem(Transform item)
        {
            currentWaveSpawnedItemCount++;
            
            // Track enemy for wave completion
            if (item != null)
            {
                int enemyId = item.GetInstanceID();
                spawnedEnemyIds.Add(enemyId);
                
                // Trigger events
                OnEnemySpawned?.Invoke(item);
                PublishEnemySpawnedEvent(item);

                if (enableDebugLogs)
                {
                    Debug.Log($"[WaveSpawnController] Enemy spawned: {item.name} (ID: {enemyId}), Total: {currentWaveSpawnedItemCount}");
                }
            }
        }

        protected override void OnControllerDestroyedItem(Transform item)
        {
            currentWaveDestroyedItemCount++;
            
            if (item != null)
            {
                int enemyId = item.GetInstanceID();
                killedEnemyIds.Add(enemyId);

                if (enableDebugLogs)
                {
                    Debug.Log($"[WaveSpawnController] Enemy destroyed: {item.name} (ID: {enemyId}), Total: {currentWaveDestroyedItemCount}");
                }

                // Check for wave completion
                CheckWaveCompletion();
            }
        }

        private void SubscribeToEnemyDeathEvents()
        {
            if (unifiedEventBus != null)
            {
                unifiedEventBus.Subscribe<EnemyDeathEvent>(OnEnemyDeathUnified);
            }
        }

        private void UnsubscribeFromEnemyDeathEvents()
        {
            if (unifiedEventBus != null)
            {
                unifiedEventBus.Unsubscribe<EnemyDeathEvent>(OnEnemyDeathUnified);
            }
        }

        private void OnEnemyDeathUnified(EnemyDeathEvent deathEvent)
        {
            // Track enemy death for wave completion
            killedEnemyIds.Add(deathEvent.EnemyId);
            CheckWaveCompletion();

            if (enableDebugLogs)
            {
                Debug.Log($"[WaveSpawnController] Enemy death event: {deathEvent.EnemyType} (ID: {deathEvent.EnemyId})");
            }
        }

        private void CheckWaveCompletion()
        {
            // Check if all spawned enemies have been killed
            if (spawnedEnemyIds.Count > 0 && killedEnemyIds.IsSupersetOf(spawnedEnemyIds))
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[WaveSpawnController] Wave {CurrentWave} completed - all enemies eliminated");
                }
                
                OnWaveEndedInternal();
            }
        }
        #endregion

        #region Unified Event System Integration
        /// <summary>
        /// Publish unified wave started event with enhanced data
        /// </summary>
        private void PublishWaveStartedEvent(int waveNumber)
        {
            if (unifiedEventBus != null)
            {
                int estimatedEnemyCount = currentWaveState?.WaveSpawnCount ?? 0;
                float difficultyMultiplier = 1.0f + (waveNumber * 0.1f);

                var waveEvent = new WaveStartedEvent(
                    waveNumber,
                    estimatedEnemyCount,
                    difficultyMultiplier,
                    0f, // wave duration (unknown at start)
                    GetCurrentWaveEnemyTypes(),
                    "WaveSpawnController"
                );
                unifiedEventBus.Publish(waveEvent);

                // Also publish music change event for wave progression
                PublishMusicChangeEvent(waveNumber);

                if (enableDebugLogs)
                {
                    Debug.Log($"[WaveSpawnController] Published WaveStartedEvent: Wave {waveNumber}, Enemies: {estimatedEnemyCount}, Difficulty: {difficultyMultiplier:F1}x");
                }
            }
        }

        /// <summary>
        /// Get enemy types for current wave
        /// </summary>
        private string[] GetCurrentWaveEnemyTypes()
        {
            if (currentWaveState?.TargetSpawnable != null)
            {
                return new string[] { currentWaveState.TargetSpawnable.ToString() };
            }
            return new string[0];
        }

        /// <summary>
        /// Publish unified enemy spawned event
        /// </summary>
        private void PublishEnemySpawnedEvent(Transform enemyTransform)
        {
            if (unifiedEventBus != null && enemyTransform != null)
            {
                var spawnEvent = new EnemySpawnedEvent(
                    enemyTransform.GetInstanceID(),
                    enemyTransform.name,
                    enemyTransform.position,
                    CurrentWave,
                    1f, // default difficulty multiplier
                    enemyTransform.gameObject,
                    "WaveSpawnController"
                );
                unifiedEventBus.Publish(spawnEvent);
            }
        }

        /// <summary>
        /// Publish unified wave completed event with enhanced metrics
        /// </summary>
        private void PublishWaveCompletedEvent()
        {
            if (unifiedEventBus != null)
            {
                float waveDuration = waveStartTime > 0 ? Time.time - waveStartTime : 0f;
                int enemiesEscaped = Mathf.Max(0, currentWaveSpawnedItemCount - currentWaveDestroyedItemCount);
                int bonusScore = CalculateWaveBonus(waveDuration, currentWaveDestroyedItemCount, enemiesEscaped);

                var waveEvent = new WaveCompletedEvent(
                    CurrentWave,
                    waveDuration,
                    currentWaveDestroyedItemCount,
                    enemiesEscaped,
                    bonusScore,
                    "WaveSpawnController"
                );
                unifiedEventBus.Publish(waveEvent);

                if (enableDebugLogs)
                {
                    Debug.Log($"[WaveSpawnController] Published WaveCompletedEvent: Wave {CurrentWave}, Duration: {waveDuration:F1}s, Killed: {currentWaveDestroyedItemCount}, Escaped: {enemiesEscaped}, Bonus: {bonusScore}");
                }
            }
        }

        /// <summary>
        /// Calculate wave completion bonus based on performance
        /// </summary>
        private int CalculateWaveBonus(float duration, int enemiesKilled, int enemiesEscaped)
        {
            int baseScore = enemiesKilled * 10;

            // Time bonus (faster completion = higher bonus)
            float timeBonus = duration > 0 ? Mathf.Max(0, (60f - duration) / 60f) : 0f;

            // Accuracy bonus (fewer escaped enemies = higher bonus)
            float accuracyBonus = enemiesKilled > 0 ? (float)enemiesKilled / (enemiesKilled + enemiesEscaped) : 0f;

            return Mathf.RoundToInt(baseScore * (1f + timeBonus * 0.5f + accuracyBonus * 0.3f));
        }

        /// <summary>
        /// Publish music change event for wave progression
        /// CRITICAL: This method should NOT determine music sections - that's SceneManagerBTR's job
        /// This creates conflicts with the Ouroboros.asset configuration
        /// </summary>
        private void PublishMusicChangeEvent(int waveNumber)
        {
            if (enableDebugLogs)
            {
                Debug.LogWarning($"[WaveSpawnController] INTEGRATION_ISSUE: PublishMusicChangeEvent called for wave {waveNumber}");
                Debug.LogWarning($"[WaveSpawnController] This conflicts with SceneManagerBTR's music section management!");
                Debug.LogWarning($"[WaveSpawnController] Consider removing this method and letting SceneManagerBTR handle music changes");
            }

            // TEMPORARILY DISABLED to prevent conflicts with SceneManagerBTR
            // The SceneManagerBTR should handle music changes based on Ouroboros.asset configuration
            /*
            if (unifiedEventBus != null)
            {
                string musicSection = GetMusicSectionForWave(waveNumber);

                var musicEvent = new AudioMusicChangeRequestEvent(
                    musicSection,
                    "previous_track", // previous track ID
                    1.0f, // fade out duration
                    1.0f, // fade in duration
                    false, // should loop
                    0.0f, // start time
                    "WaveSpawnController.MusicSync"
                );
                unifiedEventBus.Publish(musicEvent);

                if (enableDebugLogs)
                {
                    Debug.Log($"[WaveSpawnController] Published music change request: {musicSection} for Wave {waveNumber}");
                }
            }
            */
        }

        /// <summary>
        /// Determine appropriate music section for wave number
        /// DEPRECATED: This conflicts with SceneManagerBTR's section management
        /// </summary>
        private string GetMusicSectionForWave(int waveNumber)
        {
            if (enableDebugLogs)
            {
                Debug.LogWarning($"[WaveSpawnController] DEPRECATED: GetMusicSectionForWave called - this should be handled by SceneManagerBTR");
            }
            
            // Simple progression logic - can be enhanced based on game design
            if (waveNumber <= 3)
                return "Intro";
            else if (waveNumber <= 7)
                return "Build";
            else if (waveNumber <= 12)
                return "Intense";
            else
                return "Climax";
        }
        #endregion

        #region Wave End Handling
        /// <summary>
        /// Enhanced wave end method that publishes unified events
        /// </summary>
        private void OnWaveEndedInternal()
        {
            // Publish unified wave completed event
            PublishWaveCompletedEvent();
            
            // Trigger existing Unity event for backward compatibility
            OnWaveEnded?.Invoke();

            if (enableDebugLogs)
            {
                Debug.Log($"[WaveSpawnController] Wave {CurrentWave} ended");
            }
        }

        /// <summary>
        /// Public method to trigger wave completion (can be called from UltimateSpawner Event nodes)
        /// </summary>
        public void TriggerWaveCompleted()
        {
            OnWaveEndedInternal();
        }

        /// <summary>
        /// Trigger custom wave event (for WaveEventSubscriptions compatibility)
        /// </summary>
        public void TriggerCustomEvent(string eventName)
        {
            OnWaveCustomEvent?.Invoke(eventName);

            if (enableDebugLogs)
            {
                Debug.Log($"[WaveSpawnController] Custom event triggered: {eventName}");
            }
        }
        #endregion

        #region Debug Methods
        public void Log(string msg)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[WaveSpawnController] {msg}");
            }
        }
        #endregion
    }
}