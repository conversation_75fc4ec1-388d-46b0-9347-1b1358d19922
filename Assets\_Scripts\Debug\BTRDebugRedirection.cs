using UnityEngine;

namespace BTR.Debug
{
    /// <summary>
    /// BTR.Debug redirection system - provides BTR.Debug.Log, Log<PERSON><PERSON><PERSON>, LogWarning
    /// that redirect to Unity's Debug system for backward compatibility
    /// </summary>
    public static class Log
    {
        /// <summary>
        /// Logs a message to Unity's console
        /// </summary>
        /// <param name="message">Message to log</param>
        public static void Write(object message)
        {
            UnityEngine.Debug.Log(message);
        }

        /// <summary>
        /// Logs a message with context to Unity's console
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="context">Context object</param>
        public static void Write(object message, Object context)
        {
            UnityEngine.Debug.Log(message, context);
        }
    }

    /// <summary>
    /// BTR.Debug.LogError redirection class
    /// </summary>
    public static class LogError
    {
        /// <summary>
        /// Logs an error message to Unity's console
        /// </summary>
        /// <param name="message">Error message to log</param>
        public static void Write(object message)
        {
            UnityEngine.Debug.LogError(message);
        }

        /// <summary>
        /// Logs an error message with context to Unity's console
        /// </summary>
        /// <param name="message">Error message to log</param>
        /// <param name="context">Context object</param>
        public static void Write(object message, Object context)
        {
            UnityEngine.Debug.LogError(message, context);
        }
    }

    /// <summary>
    /// BTR.Debug.LogWarning redirection class
    /// </summary>
    public static class LogWarning
    {
        /// <summary>
        /// Logs a warning message to Unity's console
        /// </summary>
        /// <param name="message">Warning message to log</param>
        public static void Write(object message)
        {
            UnityEngine.Debug.LogWarning(message);
        }

        /// <summary>
        /// Logs a warning message with context to Unity's console
        /// </summary>
        /// <param name="message">Warning message to log</param>
        /// <param name="context">Context object</param>
        public static void Write(object message, Object context)
        {
            UnityEngine.Debug.LogWarning(message, context);
        }
    }
}